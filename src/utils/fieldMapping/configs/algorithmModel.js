/**
 * 算法模型字段映射配置
 * 定义宽表字段与业务字段的映射关系
 */

/**
 * 算法模型字段映射配置
 */
export const algorithmModelFieldMap = {
  // 基础信息字段
  id: 'id', // ID保持不变
  code: 'value1', // 算法编码
  name: 'value2', // 算法名称
  type: 'value3', // 算法类型
  description: 'value4', // 算法描述
  version: 'value5', // 版本号
  status: 'value6', // 状态

  // 统计信息字段
  usageCount: {
    field: 'value7',
    transform: (value) => value,
    reverseTransform: (value) => value,
    defaultValue: null
  },
  paramCount: {
    field: 'value8',
    transform: (value) => value,
    reverseTransform: (value) => value,
    defaultValue: null
  },

  // 用户信息字段
  creator: 'value9', // 创建人
  createTime: 'value10', // 创建时间
  updateTime: 'value11', // 修改时间

  // 扩展字段（JSON格式存储）
  algorithmConfig: {
    field: 'value12',
    transform: (value) => {
      try {
        return value ? JSON.parse(value) : {}
      } catch (error) {
        console.warn('算法配置解析失败:', error)
        return {}
      }
    },
    reverseTransform: (value) => {
      try {
        return JSON.stringify(value || {})
      } catch (error) {
        console.warn('算法配置序列化失败:', error)
        return '{}'
      }
    },
    defaultValue: {}
  },

  // 参数配置（JSON格式存储）
  paramConfig: {
    field: 'value13',
    transform: (value) => {
      try {
        return value ? JSON.parse(value) : []
      } catch (error) {
        console.warn('参数配置解析失败:', error)
        return []
      }
    },
    reverseTransform: (value) => {
      try {
        return JSON.stringify(value || [])
      } catch (error) {
        console.warn('参数配置序列化失败:', error)
        return '[]'
      }
    },
    defaultValue: []
  },

  // 性能指标（JSON格式存储）
  performanceMetrics: {
    field: 'value14',
    transform: (value) => {
      try {
        return value ? JSON.parse(value) : {}
      } catch (error) {
        console.warn('性能指标解析失败:', error)
        return {}
      }
    },
    reverseTransform: (value) => {
      try {
        return JSON.stringify(value || {})
      } catch (error) {
        console.warn('性能指标序列化失败:', error)
        return '{}'
      }
    },
    defaultValue: {}
  },

  // 预留扩展字段
  extField1: 'value15',
  extField2: 'value16',
  extField3: 'value17',
  extField4: 'value18',
  extField5: 'value19',

  // 系统字段
  remark: 'value20', // 备注
  sortOrder: {
    field: 'value21',
    transform: (value) => parseInt(value) || 0,
    reverseTransform: (value) => String(value || 0),
    defaultValue: 0
  }
}

/**
 * 算法模型完整配置
 */
export const algorithmModelConfig = {
  name: 'algorithmModel',
  fieldMap: algorithmModelFieldMap,

  // 数据验证规则
  validation: {
    code: {
      required: true,
      pattern: /^[A-Z][A-Z0-9_]*$/,
      message: '算法编码必须以大写字母开头，只能包含大写字母、数字和下划线'
    },
    name: {
      required: true,
      minLength: 2,
      maxLength: 50,
      message: '算法名称长度必须介于 2 和 50 之间'
    },
    type: {
      required: true,
      message: '请选择算法类型'
    },
    version: {
      required: true,
      pattern: /^\d+\.\d+\.\d+$/,
      message: '版本号格式不正确，应为 x.x.x 格式'
    },
    status: {
      required: true,
      message: '请选择状态'
    }
  },

  // 默认值配置
  defaults: {
    usageCount: 0,
    paramCount: 0,
    status: 'disabled',
    algorithmConfig: {},
    paramConfig: [],
    performanceMetrics: {},
    sortOrder: 0
  }
}

/**
 * 获取字段显示名称映射
 */
export const fieldDisplayNames = {
  id: 'ID',
  code: '算法编码',
  name: '算法名称',
  type: '算法类型',
  description: '算法描述',
  version: '版本号',
  status: '状态',
  usageCount: '使用次数',
  paramCount: '参数量',
  creator: '创建人',
  createTime: '创建时间',
  updateTime: '修改时间',
  algorithmConfig: '算法配置',
  paramConfig: '参数配置',
  performanceMetrics: '性能指标',
  remark: '备注',
  sortOrder: '排序'
}

/**
 * 获取字段的显示名称
 * @param {string} fieldName - 字段名
 * @returns {string} 显示名称
 */
export function getFieldDisplayName(fieldName) {
  return fieldDisplayNames[fieldName] || fieldName
}

/**
 * 获取所有业务字段名
 * @returns {Array<string>} 业务字段名列表
 */
export function getBusinessFields() {
  return Object.keys(algorithmModelFieldMap)
}

/**
 * 获取所有数据库字段名
 * @returns {Array<string>} 数据库字段名列表
 */
export function getDbFields() {
  return Object.values(algorithmModelFieldMap).map(mapping => {
    return typeof mapping === 'string' ? mapping : mapping.field
  })
}

export default algorithmModelConfig
