.el-radio-label-none {
  .el-radio__label {
    @apply !hidden;
  }
}

.el-upload-hidden .el-upload {
  @apply !hidden;
}

.el-form-info {
  @apply border border-gray-200;

  .el-form {
    @apply -mr-px -mb-px;
    .el-form-item {
      @apply hover:bg-gray-100 border border-gray-200 mb-0 -ml-px -mt-px px-2;

      .el-form-item__label-wrap {
        @apply !ml-auto;
      }

      .el-form-item__label {
        @apply !w-auto !font-medium truncate max-w-56;
      }
      .el-form-item__content {
        @apply !ml-auto !font-bold;
      }
    }
  }
}

.el-form-search {
  .el-form-item:not(:last-child) {
    @apply border border-gray-200 rounded pl-3;
    .el-form-item__label {
      font-size: 12px !important;
      @apply font-medium;
    }

    * {
      @apply !border-none;
    }

    .el-range__icon {
      @apply hidden;
    }
  }

  /* 整洁模式 */
  &.el-form-search--neat {
    @apply flex flex-wrap items-center;

    .el-form-item {
      @apply flex-none;
    }

    .el-form-item:not(:last-child) {
      @apply inline-flex w-56;

      &::before,
      &::after,
      .el-form-item__label {
        @apply flex-none;
      }

      .el-form-item__content {
        @apply w-0 flex-1;

        > div:not(.vue-treeselect) {
          @apply w-full;
        }
      }

      &:has(.el-date-editor.el-date-editor--daterange) {
        @apply w-[280px];
      }

      &:has(.el-date-editor.el-date-editor--datetime) {
        @apply w-[265px];
      }

      &:has(.el-date-editor.el-date-editor--datetimerange) {
        @apply !w-[410px];
      }
    }
  }
}

.el-dialog__wrapper,
.el-dialog--beautify {
  .el-dialog {
    @apply !overflow-hidden !rounded-lg !w-[96%] !2xl:w-[80%];
  }

  .el-dialog__header {
    @apply border-b border-gray-200 !pt-[14px] !pb-[16px];
  }

  .el-dialog__title {
    @apply relative !font-bold !tracking-widest;

    &::after {
      content: '';
      @apply absolute inset-x-0 bottom-0 h-[8px] bg-primary-500/30;
    }
  }

  .el-dialog__close {
    @apply !text-[22px] !-mt-[100%];
  }

  .el-dialog__footer {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16) !important;
    @apply !py-[12px];
  }
}

.el-table--beautify {
  @apply !overflow-hidden !rounded-lg;

  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    @apply !overflow-hidden !rounded-lg;

    th {
      background-color: #eef0f2 !important;
      @apply !border-b-0 !font-medium;
    }
  }

  th,
  td,
  .el-button {
    font-size: 14px !important;
  }

  th,
  td {
    color: #3e4040 !important;
  }
}

.el-tabs--single {
  .el-tabs__content {
    @apply !hidden;
  }
}

.el-tabs__new-tab--highlight {
  .el-tabs__new-tab {
    @apply !border !border-primary-500 !text-primary-500 opacity-60 hover:opacity-80 !active:opacity-100;
  }
}

.el-tabs__new-tab--hidden {
  .el-tabs__new-tab {
    @apply hidden;
  }
}

.el-form-item__table.el-form-item {
  @apply !w-full !my-0;

  .el-form-item__content {
    @apply !w-full;
  }

  .el-form-item__error {
    @apply !text-[12px] !top-[25%] !left-auto !right-2 bg-white/70;
  }
}

.el-timeline.el-timeline--primary {
  .el-timeline-item__node {
    @apply bg-primary-500;
  }
}

.el-tag {
  &.el-tag--text,
  .el-tag--text & {
    @apply !bg-transparent !border-none !p-0 !text-sm !text-[#3e4040];
  }
}
