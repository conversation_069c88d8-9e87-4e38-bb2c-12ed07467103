import { cloneDeep } from 'lodash-es'

export const searchMixin = () => {
  return {
    data() {
      const self = {
        load: this.getTableData,

        model: {},
        lazyModel: {},
        defaultModel: {},

        init: (value = {}) => {
          self.model = cloneDeep(value)
          self.lazyModel = cloneDeep(value)
          self.defaultModel = cloneDeep(value)

          return self
        },

        query: async() => {
          self.lazyModel = cloneDeep(self.model)

          await self.load({ type: 'query', model: self.model, lazyModel: self.lazyModel })

          return self
        },

        reset: async() => {
          self.init(self.defaultModel)

          await self.load({ type: 'reset', model: self.model, lazyModel: self.lazyModel })

          return self
        },

        parameter: (args = {}) => {
          const model = args?.lazy ? self.lazyModel : self.model

          if (this.sheetMixin) {
            return this.sheetMixin.parameter(model, { scope: 'search' })
          }

          return model
        }
      }

      return {
        searchMixin: self
      }
    }
  }
}

export default searchMixin
