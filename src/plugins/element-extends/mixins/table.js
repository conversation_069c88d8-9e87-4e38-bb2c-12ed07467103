import { get } from 'lodash-es'

export const tableMixin = (options = {}) => {
  const { refKey = 'tableRef', rowKey = 'id' } = options

  return {
    computed: {
      tableMixin_selectionIds() {
        return this.tableMixin.selectionIds
      },
      tableMixin_data() {
        return this.tableMixin.data
      }
    },
    data() {
      const self = {
        refKey,
        rowKey,

        api: {},
        loading: false,
        data: [],

        // multiple | single
        selectionType: '',
        selection: [],

        selected: false,
        singleSelected: false,
        multipleSelected: false,

        selectionIds: [],

        currentRowKey: void 0,
        currentRow: void 0,

        getTableRef: () => get(this, self.refKey),

        getRowId: (row) => row[self.rowKey],

        onSelectionChange: (selection) => {
          this.$emit('selection-change', selection)

          if (['single'].includes(self.selectionType)) return

          self.selection = selection

          self.selected = !!selection.length
          self.singleSelected = selection.length === 1
          self.multipleSelected = selection.length > 1

          self.selectionIds = selection.map((item) => self.getRowId(item))
        },

        onRowClick: (currentRow) => {
          this.$emit('row-click', currentRow)

          if (['multiple'].includes(self.selectionType)) return

          self.getTableRef().setCurrentRow(currentRow)
          self.currentRowKey = self.getRowId(currentRow)
          self.currentRow = currentRow
          self.selection = [currentRow]
          self.selectionIds = [self.getRowId(currentRow)]
        },

        echoSelection: async(ids) => {
          await this.$nextTick()

          const idList = (typeof ids === 'string' ? ids.split(',') : ids).filter((item) => !!item)

          if (['single'].includes(self.selectionType)) {
            const id = idList[0]
            self.echoCurrentRow(id)
            return false
          }

          self.echoRowSelection(idList)
        },

        echoRowSelection: (ids = []) => {
          if (!ids?.length) return false

          for (let index = 0; index < self.data.length; index++) {
            const row = self.data[index]
            const rowId = self.getRowId(row)

            const selected = [...ids, ...self.selectionIds].includes(rowId)

            self.getTableRef().toggleRowSelection(row, selected)
          }
        },

        echoCurrentRow: (id = '') => {
          const findRow = self.data.find((row) => self.getRowId(row) === id)

          if (!findRow) {
            return false
          }

          self.onRowClick(findRow)
        },
        getTableData: async(args = {}) => {
          const { dataKey = 'rows', addParams = {}, formatter = (value) => value } = args

          if (['query', 'reset'].includes(args.type)) {
            await this.$nextTick()
            self.getTableRef().clearSelection()
          }

          const params = {}

          if (this.searchMixin) {
            Object.assign(params, {
              ...this.searchMixin.parameter()
            })
          }

          if (this.pagingMixin) {
            Object.assign(params, {
              ...this.pagingMixin.parameter()
            })
          }

          Object.assign(params, { ...addParams })

          let res
          let catchError

          self.loading = true

          try {
            res = await self.api.list(params)
          } catch (error) {
            catchError = error
          }

          self.loading = false

          if (catchError) {
            throw catchError
          }

          const data = res[dataKey] || []

          self.data = data.map(formatter)

          if (this.pagingMixin) {
            this.pagingMixin.setTotal(res.total || 0)
          }

          return {
            params,
            data
          }
        }
      }

      return {
        tableMixin: self
      }
    }
  }
}

export default tableMixin
