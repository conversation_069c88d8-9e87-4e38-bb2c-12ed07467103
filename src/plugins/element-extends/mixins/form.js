export const formMixin = (options = {}) => {
  return {
    data() {
      const self = {
        loading: false,
        idKey: 'id',
        params: {},
        data: {},
        api: {},
        success: (type, res) => this.$emit(`${type}-success`, res),

        formatter: (info) => {
          const value = Object.keys(info).reduce((obj, key) => {
            const config = this.sheetMixin.getMapModel()[key]
            const formatter = this.sheetMixin.getNestedValue(config, 'formatter')

            if (formatter) {
              obj[key] = formatter(info)
            }

            return obj
          }, {})

          return value
        },

        get: async(addParams = {}) => {
          if (!self.api?.info) {
            return false
          }

          const params = {
            ...self.params,
            ...addParams
          }

          const id = params[self.idKey]

          let res
          let catchError

          self.loading = true

          try {
            res = await self.api.info(id, params)
          } catch (error) {
            catchError = error
          }

          self.loading = false

          if (catchError) {
            throw catchError
          }

          self.data = res.data || {}

          if (this.sheetMixin) {
            Object.assign(self.data, self.formatter(self.data))
          }

          this.$emit('form-info-success', res)
        },

        add: async(addParams = {}, args = {}) => {
          const params = {
            ...self.params,
            ...self.data,
            ...addParams
          }

          if (this.sheetMixin) {
            Object.assign(params, {
              ...this.sheetMixin.parameter(params, { scope: args?.scope })
            })
          }

          let res
          let catchError

          self.loading = true

          try {
            res = await self.api.add(params)
          } catch (error) {
            catchError = error
          }

          self.loading = false

          if (catchError) {
            throw catchError
          }

          this.$message.success(res.msg)
          self.success?.('add', res)
        },

        edit: async(addParams = {}, args = {}) => {
          const params = {
            ...self.params,
            ...self.data,
            ...addParams
          }

          if (this.sheetMixin) {
            Object.assign(params, {
              ...this.sheetMixin.parameter(params, { scope: args?.scope })
            })
          }

          let res
          let catchError

          self.loading = true

          try {
            res = await self.api.edit(params)
          } catch (error) {
            catchError = error
          }

          self.loading = false

          if (catchError) {
            throw catchError
          }

          this.$message.success(res.msg)
          self.success?.('edit', res)
        },

        reset: () => {
          self.loading = false
          self.data = this.$options.data().formMixin.data
          self.params = this.$options.data().formMixin.params
        }
      }

      return {
        formMixin: self
      }
    }
  }
}

export default formMixin
