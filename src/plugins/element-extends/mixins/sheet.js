import { isNil, omitBy } from 'lodash-es'

import { createProp, getNestedValue, shouldRenderField, normalizeOptions } from '@/plugins/element-extends/helper.js'

export { getNestedValue, shouldRenderField }

export const sheetMixin = (options = {}) => {
  const { scope, modelKey = 'model', separator = ':' } = options

  return {
    props: {
      api: {
        type: Object,
        default: () => ({})
      },
      [modelKey]: {
        type: [Array, Object],
        default: () => []
      },
      layout: {
        type: [String, Array, Object],
        default: ''
      },
      idKey: {
        type: String,
        default: 'id'
      },
      hiddenActions: {
        type: [String, Array, Object],
        default: () => []
      },
      lazy: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      sheetMixin_formatModel() {
        const self = this.sheetMixin

        const value = Array.isArray(this.$props[self.modelKey])
          ? this.$props[self.modelKey]
          : Object.entries(this.$props[self.modelKey]).reduce((arr, [key, item], index) => {
            arr.push({
              ...item,
              [self.fieldKey]: key,
              sort: item.sort ?? (index + 1) * 10
            })

            return arr
          }, [])

        return value
      },
      sheetMixin_mapModel() {
        const self = this.sheetMixin

        const model = self.getFormatModel()

        const value = model.reduce((obj, item) => {
          const field = item[self.fieldKey]

          obj[field] = item

          return obj
        }, {})

        return value
      },
      sheetMixin_layoutOptions() {
        const value = normalizeOptions(this.$props.layout)

        return value
      },
      sheetMixin_actionOptions() {
        const value = normalizeOptions(this.$props.hiddenActions, { showKey: 'hidden' })

        return value
      }
    },
    data() {
      const self = {
        modelKey,
        scope,
        separator,
        fieldKey: 'field',

        getMapModel: () => {
          return this.sheetMixin_mapModel
        },

        getFormatModel: () => {
          return this.sheetMixin_formatModel
        },

        getSlotModel: (...args) => {
          const fieldTemplate = `<${self.fieldKey}-value>`

          if (!args.includes(fieldTemplate)) {
            args.push(fieldTemplate)
          }

          const replaceField = (data) => {
            const value = args.reduce((arr, item) => {
              if (item === fieldTemplate) {
                arr.push(data[self.fieldKey])
              } else {
                arr.push(item)
              }
              return arr
            }, [])

            return value
          }

          const value = self.getFormatModel().filter((item) => {
            return self.getFieldSlot(...replaceField(item))
          })

          return value
        },

        getRenderModel: (scope = self.scope) => {
          const value = self
            .getFormatModel()
            .filter((item) => shouldRenderField(getNestedValue(item, 'hidden', scope)))
            .sort(
              (a, b) => getNestedValue(a, 'sort', scope) - getNestedValue(b, 'sort', scope)
            )

          return value
        },

        getFieldSlot: (...args) => {
          const slotKey = self.separatorJoin(...args)
          return this.$scopedSlots[slotKey]
        },

        getNestedValue: (...args) => {
          const [data, field, scope = self.scope] = args

          return getNestedValue(data, field, scope)
        },

        hasLayout: (type) => {
          const value = this.sheetMixin_layoutOptions[type]?.show

          return value
        },

        hasAction: (type) => {
          const value = this.$props.api[type] && !this.sheetMixin_actionOptions[type]?.hidden

          return value
        },

        separatorJoin: (...args) => {
          return args.filter((item) => !!item).join(self.separator)
        },

        createProps(data, { deepKeys, ...model } = {}) {
          const keys = Object.keys(model)

          const values = keys.reduce((obj, key) => {
            const value = self.getNestedValue(data, key)

            if (value !== void 0) {
              obj[key] = value
            }

            return obj
          }, {})

          const scopeDeepKeys = typeof deepKeys === 'string' ? deepKeys.split(',') : deepKeys

          const deepValues = {}

          for (let index = 0; index < scopeDeepKeys.length; index++) {
            const key = scopeDeepKeys[index]
            const value = createProp(self.getNestedValue(data, key))
            Object.assign(deepValues, { ...value })
          }

          return {
            ...omitBy(model, isNil),
            ...values,
            ...deepValues
          }
        },

        getFieldProps: (data, { deepKeys = ['fieldProps'] } = {}) => {
          const props = this.sheetMixin.createProps(data, {
            type: void 0,
            options: void 0,
            disabled: void 0,
            remoteDict: void 0,
            dict: void 0,
            deepKeys
          })

          const remoteDict = props.remoteDict
          const dictType = remoteDict || props.dict
          const remote = !!remoteDict
          const dictProps = dictType
            ? {
              dictType,
              remote
            }
            : {}

          const value = {
            ...dictProps,
            ...props
          }

          return value
        },
        getFieldListeners(info, args = {}) {
          const { ctx } = args

          const fieldProps = self.getFieldProps(info)

          if (!fieldProps.on) {
            return {}
          }

          Object.entries(fieldProps.on).forEach(([key, event]) => {
            fieldProps.on[key] = (...args) => event(...args, ctx)
          })

          return fieldProps.on
        },
        parameter: (data = {}, args = {}) => {
          const mapModel = self.getMapModel()

          const value = Object.entries(data).reduce((obj, [key, value]) => {
            const parameter = self.getNestedValue(mapModel[key], 'parameter', args?.scope)

            if (parameter) {
              const parameterValue = parameter(value, data)

              if (typeof parameterValue === 'object' && !Array.isArray(parameterValue)) {
                Object.assign(obj, { ...parameterValue })
              } else {
                obj[key] = parameterValue
              }
            } else {
              obj[key] = value
            }

            return obj
          }, {})

          return value
        },
        getModelValue: (scope = self.scope) => {
          const value = self.getFormatModel().reduce((obj, item) => {
            obj[item.field] = getNestedValue(item, 'value', scope)
            return obj
          }, {})

          return value
        }
      }

      return {
        sheetMixin: self
      }
    }
  }
}

export default sheetMixin
