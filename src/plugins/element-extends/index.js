import EleTagDict from './components/EleTagDict/index.vue'
import EleSelectDict from './components/EleField/SelectDict/index.vue'
import EleDatePickerRange from './components/EleDatePickerRange/index.vue'
import EleTooltipButton from './components/EleTooltipButton/index.vue'
import EleUploadDialog from './components/EleUploadDialog/index.vue'
import EleFormSearch from './components/EleFormSearch/index.vue'
import EleFormRow from './components/EleFormRow/index.vue'
import EleFormItemCol from './components/EleFormItemCol/index.vue'
import EleField from './components/EleField/index.vue'
import EleTree from './components/EleTree/index.vue'

import EleSheet from './components/EleSheet/index.vue'
import EleSheetToolbar from './components/EleSheetToolbar/index.vue'
import EleSheetSearch from './components/EleSheetSearch/index.vue'
import EleSheetTable from './components/EleSheetTable/index.vue'
import EleSheetPaging from './components/EleSheetPaging/index.vue'
import EleSheetImport from './components/EleSheetImport/index.vue'
import EleSheetForm from './components/EleSheetForm/index.vue'
import EleSheetInfo from './components/EleSheetInfo/index.vue'

import './styles/index.css'

export default {
  install(Vue) {
    Vue.component('EleTagDict', EleTagDict)
    Vue.component('EleSelectDict', EleSelectDict)
    Vue.component('EleDatePickerRange', EleDatePickerRange)
    Vue.component('EleTooltipButton', EleTooltipButton)
    Vue.component('EleUploadDialog', EleUploadDialog)
    Vue.component('EleFormRow', EleFormRow)
    Vue.component('EleFormItemCol', EleFormItemCol)
    Vue.component('EleFormSearch', EleFormSearch)
    Vue.component('EleField', EleField)
    Vue.component('EleTree', EleTree)

    Vue.component('EleSheet', EleSheet)
    Vue.component('EleSheetToolbar', EleSheetToolbar)
    Vue.component('EleSheetSearch', EleSheetSearch)
    Vue.component('EleSheetTable', EleSheetTable)
    Vue.component('EleSheetPaging', EleSheetPaging)
    Vue.component('EleSheetImport', EleSheetImport)
    Vue.component('EleSheetForm', EleSheetForm)
    Vue.component('EleSheetInfo', EleSheetInfo)
  }
}
