<template>
  <el-tree
    v-if="showTree"
    ref="treeRef"
    v-slot="{ node, data }"
    class="ele-tree"
    :style="varStyle"
    v-bind="{ ...$attrs, highlightCurrent }"
    v-on="$listeners"
  >
    <div style="width: 100%">
      <slot v-bind="{ node, data }" />
    </div>
  </el-tree>
</template>

<script>
import { inheritComponentMethods } from '@/plugins/element-extends/helper.js'

export default {
  props: {
    nodeHeight: {
      type: String,
      default: '26px'
    },
    highlightCurrent: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showTree: true
    }
  },
  computed: {
    varStyle() {
      return `--tree-node-height: ${this.nodeHeight}`
    }
  },
  methods: {
    // 继承树方法
    ...inheritComponentMethods('treeRef', [
      'filter',
      'updateKeyChildren',
      'getCheckedNodes',
      'setCheckedNodes',
      'getCheckedKeys',
      'setCheckedKeys',
      'setChecked',
      'getHalfCheckedNodes',
      'getHalfCheckedKeys',
      'getCurrentKey',
      'getCurrentNode',
      'setCurrentKey',
      'setCurrentNode',
      'getNode',
      'remove',
      'append',
      'insertBefore',
      'insertAfter'
    ]),
    async setCurrentNodeExtend(params, { expanded = true } = {}) {
      const nodeKey = this.$refs.treeRef.nodeKey
      const keyValue = typeof params === 'string' ? params : params?.[nodeKey]
      if (!keyValue) {
        return
      }

      this.$emit('update:current-node-key', keyValue)

      await this.$nextTick()

      this.$refs.treeRef.setCurrentKey(keyValue)
      const data = this.$refs.treeRef.getCurrentNode()
      const node = this.$refs.treeRef.getNode(keyValue)
      this.$emit('node-click', data, node)

      if (expanded) {
        const defaultExpandedKeys = this.$refs.treeRef.defaultExpandedKeys
        this.$emit('update:default-expanded-keys', [
          ...new Set([...defaultExpandedKeys, keyValue])
        ])
      }
      return {
        data,
        node
      }
    },
    async reload() {
      this.showTree = false
      await this.$nextTick()
      this.showTree = true
    }
  }
}
</script>

<style scoped lang="postcss">
  ::v-deep.ele-tree.el-tree {
    overflow-y: auto;
    overflow-x: hidden;

    .el-tree-node__content {
      height: var(--tree-node-height);
      line-height: var(--tree-node-height);
    }

    &.show-overflow-x {
      .el-tree-node > .el-tree-node__children {
        overflow: initial;
      }
      overflow-x: auto;
    }
  }
</style>
