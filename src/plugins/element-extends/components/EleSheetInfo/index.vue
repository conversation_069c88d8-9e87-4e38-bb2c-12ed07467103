<template>
  <component
    :is="headless ? 'div' : 'el-dialog'"
    class="el-dialog--beautify"
    v-bind="{
      ...$attrs,
      title: dialogMixin.title,
      appendToBody: true,
      closeOnClickModal: false,
    }"
    :visible.sync="dialogMixin.visible"
    @closed="onClosed"
  >
    <div
      v-if="showTitle"
      class="text-center pb-4 -mt-4 text-base font-sans text-gray-600 font-semibold"
    >
      {{ showTitle }}
    </div>

    <slot name="before" v-bind="{ ...formMixin, model: formMixin.data }" />

    <div class="el-form-info">
      <el-form
        v-if="dialogMixin.lazyVisible"
        ref="formRef"
        v-loading="formMixin.loading"
        :class="[customClass]"
        v-bind="{
          model: formMixin.data,
          labelSuffix: ':',
          labelWidth: 'auto',
          ...filterFormProps,
        }"
      >
        <el-row v-bind="{ ...rowProps }" type="flex" class="!flex-wrap">
          <template v-for="(item, index) of sheetMixin.getRenderModel()">
            <slot
              v-if="sheetMixin.getFieldSlot(item.field, 'before')"
              :name="sheetMixin.separatorJoin(item.field, 'before')"
              v-bind="getSlotProps(item)"
            />

            <slot
              v-if="sheetMixin.getFieldSlot(item.field)"
              :name="item.field"
              v-bind="getSlotProps(item)"
            />
            <el-col
              v-else
              :key="item.field"
              v-bind="{
                ...getColProps(item),
              }"
            >
              <el-form-item :key="item.field" v-bind="getItemProps(item)">
                <template #label>
                  <span class="relative z-10" :title="getItemProps(item).label">
                    {{ getItemProps(item).label }}:
                  </span>
                </template>

                <div class="truncate" :title="formMixin.data[item.field]">
                  <slot
                    v-if="sheetMixin.getFieldSlot(item.field, 'simple')"
                    :name="sheetMixin.separatorJoin(item.field, 'simple')"
                    v-bind="getSlotProps(item)"
                  />
                  <EleField
                    v-else
                    :key="item.field"
                    v-bind="getFieldProps(item)"
                    :value="formMixin.data[item.field]"
                  />
                </div>
              </el-form-item>
            </el-col>

            <slot
              v-if="sheetMixin.getFieldSlot(item.field, 'after')"
              :name="sheetMixin.separatorJoin(item.field, 'after')"
              v-bind="getSlotProps(item)"
            />
          </template>
        </el-row>
      </el-form>
    </div>

    <slot name="after" v-bind="{ ...formMixin, model: formMixin.data }" />
  </component>
</template>

<script>
import { sheetMixin, formMixin, dialogMixin } from '@/plugins/element-extends/mixins/index.js'

export default {
  name: 'EleSheetInfo',
  inheritAttrs: false,
  mixins: [sheetMixin({ scope: 'info' }), formMixin(), dialogMixin()],
  props: {
    formProps: {
      type: Object,
      default: () => ({})
    },
    customClass: {
      type: String,
      default: ''
    },
    headless: {
      type: Boolean,
      default: false
    },
    rowProps: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  computed: {
    showTitle() {
      if (!this.formProps.title) {
        return false
      }

      if (typeof this.formProps.title === 'boolean') {
        return '详情展示'
      }

      return this.formProps.title
    },
    filterFormProps() {
      const value = { ...this.formProps }

      delete value.title

      return value
    }
  },
  methods: {
    open(args = {}) {
      const { title, ...params } = args
      this.formMixin.idKey = this.$props.idKey
      this.formMixin.params = params
      this.formMixin.api = this.$props.api
      this.formMixin.get()

      this.dialogMixin.open(args)
    },

    close() {
      this.dialogMixin.close()
    },

    onClosed() {
      this.formMixin.reset()
      this.dialogMixin.reset()
    },

    getItemProps(item = {}) {
      const props = this.sheetMixin.createProps(item, {
        label: void 0,
        field: void 0,
        deepKeys: 'formItemProps'
      })

      const value = {
        ...props,
        model: this.formMixin.data,
        prop: props.field
      }

      return value
    },
    getColProps(item) {
      const props = this.sheetMixin.createProps(item, {
        span: 12,
        lg: 6,
        deepKeys: 'colProps'
      })

      const value = {
        ...props
      }

      return value
    },
    getFieldProps(data) {
      const value = {
        type: 'text',
        ...(this.sheetMixin.getFieldProps(data) || {}),
        preview: true
      }

      return value
    },
    getSlotProps(data) {
      const colProps = this.getColProps(data)
      const itemProps = this.getItemProps(data)

      const value = { ...itemProps, colProps, itemProps }

      return value
    }
  }
}
</script>

<style></style>
