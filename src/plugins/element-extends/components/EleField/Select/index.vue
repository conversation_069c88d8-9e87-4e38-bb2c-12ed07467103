<template>
  <el-select
    v-bind="{
      ...$attrs,
      clearable,
    }"
    v-on="{
      ...$listeners,
      change: onChange
    }"
  >
    <el-option
      v-for="(item, index) of options"
      :key="index"
      :label="item[labelKey]"
      :value="item[valueKey]"
    />
  </el-select>
</template>

<script>
export default {
  props: {
    options: {
      type: Array,
      default: () => []
    },
    labelKey: {
      type: String,
      default: 'label'
    },
    valueKey: {
      type: String,
      default: 'value'
    },
    clearable: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    onChange(value) {
      const label = this.options.find((item) => item.value == value)?.label
      this.$emit('change', value, label)
      this.$emit('label-change', label)
    }
  }
}
</script>

<style></style>
