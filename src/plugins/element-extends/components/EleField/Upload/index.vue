<template>
  <ElUpload
    v-bind="{
      ...$attrs,
      action: uploadMixin.formatAction(action),
      headers: {
        ...headers,
        ...uploadMixin.headers,
      },
      fileList,
      onSuccess,
      onRemove,
      onPreview,
    }"
    v-on="{
      ...$listeners,
    }"
  >
    <slot name="default">
      <el-button>上传文件</el-button>
    </slot>
    <slot name="trigger" />
    <slot name="tip" />
  </ElUpload>
</template>

<script>
import { uploadMixin } from '@/plugins/element-extends/mixins/index'

export default {
  mixins: [uploadMixin()],
  props: {
    value: {
      type: [Array, String, Object],
      default: () => []
    },
    action: {
      type: String,
      default: '/common/uploadMinio'
    },
    headers: {
      type: Object,
      default: () => ({})
    },
    formatter: {
      type: Function,
      default: (item) => ({ ...item, url: item?.response?.url })
    },
    successCode: {
      type: Number,
      default: 200
    }
  },
  computed: {
    fileList: {
      get() {
        if (typeof this.value === 'string') {
          return [
            {
              name: this.uploadMixin.url2name(this.value),
              url: this.value
            }
          ]
        } else if (this.value?.url) {
          return [
            {
              ...this.value,
              name: this.value.name || this.uploadMixin.url2name(this.value.url)
            }
          ]
        }

        return this.value ?? []
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  methods: {
    async onSuccess(response, file, fileList) {
      this.fileList = fileList
        .filter((item) => item.response?.code === this.successCode)
        .map((item) => this.formatter(item))

      await this.$nextTick()

      this.$emit('success', response, file, this.fileList)
    },

    async onRemove(file, fileList) {
      this.fileList = fileList

      await this.$nextTick()

      this.$emit('remove', file, this.fileList)
    },

    onPreview(file) {
      if (this.$listeners.preview) {
        this.$listeners.preview()
        return
      }

      window.open(file.url)
    }
  }
}
</script>

<style></style>
