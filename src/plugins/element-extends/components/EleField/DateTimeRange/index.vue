<template>
  <el-date-picker
    v-bind="{
      type: 'datetimerange',
      ...$attrs,
      ...$props,
    }"
    v-on="{
      ...$listeners,
    }"
  />
</template>

<script>
export default {
  props: {
    valueFormat: {
      type: String,
      default: 'yyyy-MM-dd HH:mm:ss'
    },
    startPlaceholder: {
      type: String,
      default: '开始时间'
    },
    endPlaceholder: {
      type: String,
      default: '结束时间'
    },
    rangeSeparator: {
      type: String,
      default: '-'
    },
    clearable: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style lang="postcss"></style>
