<template>
  <el-switch
    v-bind="{
      ...$attrs,
      ...optionProps,
    }"
    v-on="{
      ...$listeners,
    }"
  />
</template>

<script>

import { defaultSwitchOptions } from '@/plugins/element-extends/dicts.js'

export default {
  props: {
    options: {
      type: Array,
      default: () => []
    },
    inactiveValue: {
      type: [String, Number],
      default: void 0
    },
    inactiveText: {
      type: String,
      default: void 0
    },
    activeValue: {
      type: [String, Number],
      default: void 0
    },
    activeText: {
      type: String,
      default: void 0
    }
  },
  computed: {
    optionProps() {
      const options = this.options?.length ? this.options : defaultSwitchOptions.map(item => ({ ...item, label: '' }))

      const [active, inactive] = options

      return {
        inactiveValue: this.inactiveValue ?? inactive.value,
        inactiveText: this.inactiveText ?? inactive.label,
        activeValue: this.activeValue ?? active.value,
        activeText: this.activeText ?? active.label
      }
    }
  }
}
</script>

<style></style>
