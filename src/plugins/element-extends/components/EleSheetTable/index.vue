<template>
  <el-table
    ref="tableRef"
    :key="tableKey"
    v-loading="tableMixin.loading"
    class="el-table--beautify"
    v-bind="{
      ...$attrs,
      data: tableMixin.data,
      rowKey: tableMixin.rowKey,
      highlightCurrentRow: highlightRow && ['single'].includes(tableMixin.selectionType),
      highlightSelectionRow: highlightRow && ['multiple', true].includes(tableMixin.selectionType),
    }"
    v-on="{
      ...$listeners,
    }"
    @selection-change="tableMixin.onSelectionChange"
    @row-click="tableMixin.onRowClick"
  >
    <el-table-column
      v-if="['multiple', true].includes(tableMixin.selectionType)"
      align="center"
      width="50"
      type="selection"
      v-bind="{ ...selectionProps, reserveSelection: isReserveSelection }"
    />

    <el-table-column v-if="['single'].includes(tableMixin.selectionType)" align="center" width="50">
      <template #default="{ row, $index }">
        <div class="" @click.stop>
          <el-radio
            class="el-radio-label-none"
            :label="tableMixin.getRowId(row)"
            :value="tableMixin.currentRowKey"
            @input="() => tableMixin.onRowClick(row, $index)"
          />
        </div>
      </template>
    </el-table-column>

    <el-table-column v-if="showIndex" key="index" type="index" align="center" label="序号" />

    <slot name="before" />

    <template v-for="(item, index) of sheetMixin.getRenderModel()">
      <slot
        v-if="sheetMixin.getFieldSlot(item.field, 'before')"
        :name="sheetMixin.separatorJoin(item.field, 'before')"
        v-bind="getSlotProps(item)"
      />

      <slot
        v-if="sheetMixin.getFieldSlot(item.field)"
        :name="item.field"
        v-bind="getSlotProps(item)"
      />
      <el-table-column
        v-else
        :key="item.field"
        v-slot="{ row, $index }"
        v-bind="getColumnProps(item)"
      >
        <slot
          v-if="sheetMixin.getFieldSlot(item.field, 'simple')"
          :name="sheetMixin.separatorJoin(item.field, 'simple')"
          v-bind="{ ...getSlotProps(item), row, $index, model: row }"
        />
        <EleField
          v-else
          :key="[item.field, JSON.stringify(item.options || [])].join('-')"
          :value="showValue(row, item)"
          v-bind="getFieldProps(item)"
          v-on="sheetMixin.getFieldListeners(item, { ctx: { ...getSlotProps(item), row, $index } })"
          @click.native="handleClick(row, item)"
        />
      </el-table-column>

      <slot
        v-if="sheetMixin.getFieldSlot(item.field, 'after')"
        :name="sheetMixin.separatorJoin(item.field, 'after')"
        v-bind="getSlotProps(item)"
      />
    </template>

    <slot name="after" />

    <template #empty>
      <slot name="empty" />
    </template>

    <template #append>
      <slot name="append" />
    </template>
  </el-table>
</template>

<script>
import {
  sheetMixin,
  tableMixin,
  searchMixin,
  pagingMixin
} from '@/plugins/element-extends/mixins/index.js'
import { getTextWidth } from '@/plugins/element-extends/helper.js'

export default {
  name: 'EleSheetTable',
  inheritAttrs: false,
  mixins: [sheetMixin({ modelKey: 'model', scope: 'table' })],
  props: {
    tableMixin: {
      type: Object,
      default: () => tableMixin().data().tableMixin
    },
    searchMixin: {
      type: Object,
      default: () => searchMixin().data().searchMixin
    },
    pagingMixin: {
      type: Object,
      default: () => pagingMixin().data().pagingMixin
    },
    tableColumnProps: {
      type: Object,
      default: () => ({})
    },
    showIndex: {
      type: Boolean,
      default: false
    },
    highlightRow: {
      type: Boolean,
      default: true
    },
    selection: {
      type: [Boolean, String],
      default: false
    },
    selectionRowKeys: {
      type: [Array, String],
      default: () => []
    },
    reserveSelection: {
      type: Boolean,
      default: false
    },
    selectionProps: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  computed: {
    tableKey() {
      return ['sheet-table', this.pagingMixin.pageNum].join('-')
    },
    isReserveSelection() {
      let value = false

      if (
        this.reserveSelection ||
          this.sheetMixin.hasAction('all') ||
          this.sheetMixin.hasAction('remove') ||
          !this.$props.api.remove
      ) {
        value = true
      }

      // console.log('isReserveSelection', value);

      return value
    }
  },
  created() {
    if (this.$props.selection) {
      this.tableMixin.selectionType = this.$props.selection
    } else if (this.api.remove) {
      this.tableMixin.selectionType = 'multiple'
    }

    this.tableMixin.rowKey = this.$props.idKey
    this.tableMixin.api = this.$props.api

    if (!this.lazy) {
      this.getTableData()
    }
  },
  methods: {
    // 回显选中数据
    echoSelection() {
      this.tableMixin.echoSelection(this.selectionRowKeys)
    },
    async getTableData(...args) {
      try {
        const { data, params } = await this.tableMixin.getTableData(...args)

        this.$emit('list-success', data, params)
      } catch (error) {
        console.warn(error?.message)
        return false
      }

      this.echoSelection()
    },
    getColumnProps(data) {
      const props = this.sheetMixin.createProps(data, {
        label: void 0,
        field: void 0,
        width: void 0,
        align: 'center',
        showOverflowTooltip: true,
        deepKeys: 'tableColumnProps'
      })

      const value = {
        ...props,
        ...this.tableColumnProps,
        minWidth: getTextWidth(props.label) + 25,
        prop: props.field
      }

      return value
    },
    getFieldProps(item) {
      return {
        type: 'text',
        ...(this.sheetMixin.getFieldProps(item) || {}),
        preview: true
      }
    },
    getSlotProps(data) {
      const value = {}

      if (data) {
        const columnProps = this.getColumnProps(data)
        const fieldProps = this.getFieldProps(data)
        Object.assign(value, {
          ...columnProps,
          columnProps,
          fieldProps
        })
      }

      return value
    },
    showValue(row, item) {
      const formatter =
          this.sheetMixin.getNestedValue(item, 'formatter') ||
          this.sheetMixin.getNestedValue(item, 'tableColumnProps')?.formatter

      if (formatter) {
        return formatter(row)
      }

      return row[item.field]
    },
    handleClick(row, item) {
      const onClick = this.sheetMixin.getNestedValue(item, 'tableColumnProps')?.onClick

      if (onClick) {
        onClick(row, item)
        return
      }

      this.$emit('row-click', row, item)
      this.$emit('table-row-click', row, item)
    }
  }
}
</script>

<style></style>
