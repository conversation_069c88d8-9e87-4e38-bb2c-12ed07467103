<template>
  <div v-if="visible" class="" style="display: inline-block; padding-left: 10px">
    <el-button @click="handleToggle">{{ collapsed ? '更多' : '折叠' }}</el-button>
  </div>
</template>

<script>
import { debounce } from 'lodash-es'

export default {
  name: 'MoreAction',
  props: {
    parentRef: {
      type: Function,
      default: null
    },
    offsetWidth: {
      type: Number,
      default: 280
    },
    rows: {
      type: Number,
      default: 2
    },
    actionPropKey: {
      type: String,
      default: '$action'
    },
    autoresize: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      collapsed: true,
      visible: false
    }
  },

  async mounted() {
    await this.$nextTick()

    if (!this.parentRef) {
      return false
    }

    this.init()

    if (this.autoresize) {
      this.initResizeObserver(() => {
        this.collapsed = true
        this.init()
      })
    }
  },

  beforeDestroy() {
    this.resizeObserver?.disconnect?.()
    this.resizeObserver = null
  },

  methods: {
    init() {
      const clientWidth = this.parentRef().$el.clientWidth

      const limitWidth = clientWidth * this.rows

      const children = this.parentRef().$children

      let beforeIndexWidth = 0

      for (let index = 0; index < children.length; index++) {
        const item = children[index]

        const itemWidth = item.$el.clientWidth + 10

        const indexWidth = beforeIndexWidth + itemWidth

        beforeIndexWidth = indexWidth

        if (!this.collapsed || [this.actionPropKey].includes(item.$props.prop)) {
          item.$el.style.display = 'inline-flex'
        } else if (indexWidth > limitWidth - this.offsetWidth) {
          item.$el.style.display = 'none'
          this.visible = true
        }
      }
    },

    initResizeObserver(callback) {
      let beforeWidth = this.parentRef().$el.clientWidth

      this.resizeObserver = new ResizeObserver(
        debounce((entries) => {
          const [entry] = entries
          const { width } = entry.contentRect

          if (beforeWidth === width) {
            return false
          }

          beforeWidth = width

          callback(width)
        }, 300)
      )

      this.resizeObserver.observe(this.parentRef().$el)
    },

    handleToggle() {
      this.collapsed = !this.collapsed
      this.$emit('change', this.collapsed)
      this.init()
    }
  }
}
</script>

<style></style>
