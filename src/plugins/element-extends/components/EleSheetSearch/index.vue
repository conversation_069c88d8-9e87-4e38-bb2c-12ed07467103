<template>
  <el-form
    ref="formRef"
    v-bind="{
      ...$attrs,
      inline: true,
      model: searchMixin.model,
    }"
    :class="['el-form-search el-form-search--neat', searchClass]"
  >
    <slot name="before" v-bind="getSlotProps()" />

    <slot name="default" v-bind="getSlotProps()">
      <template v-for="(item, index) of sheetMixin.getRenderModel()">
        <slot
          v-if="sheetMixin.getFieldSlot(item.field, 'before')"
          :name="sheetMixin.separatorJoin(item.field, 'before')"
          v-bind="getSlotProps(item)"
        />

        <slot
          v-if="sheetMixin.getFieldSlot(item.field)"
          :name="item.field"
          v-bind="getSlotProps(item)"
        />
        <el-form-item v-else :key="item.field" v-bind="getItemProps(item)">
          <slot
            v-if="sheetMixin.getFieldSlot(item.field, 'simple')"
            :name="sheetMixin.separatorJoin(item.field, 'simple')"
            v-bind="getSlotProps(item)"
          />
          <EleField
            v-else
            :key="item.field"
            v-model="searchMixin.model[item.field]"
            v-bind="getFieldProps(item)"
            v-on="sheetMixin.getFieldListeners(item, { ctx: getSlotProps(item) })"
          />
        </el-form-item>

        <slot
          v-if="sheetMixin.getFieldSlot(item.field, 'after')"
          :name="sheetMixin.separatorJoin(item.field, 'after')"
          v-bind="getSlotProps(item)"
        />
      </template>
    </slot>

    <slot name="action">
      <el-form-item prop="$action" :class="[actionClass]">
        <el-button type="primary" @click="query">搜索</el-button>
        <el-button @click="reset">重置</el-button>
        <MoreAction
          :key="sheetMixin.getRenderModel().length"
          :parent-ref="() => $refs.formRef"
          @change="reset"
        />
      </el-form-item>
    </slot>

    <slot name="after" v-bind="getSlotProps()" />
  </el-form>
</template>

<script>
import { default as sheetMixin, getNestedValue } from '@/plugins/element-extends/mixins/sheet.js'

import searchMixin from '@/plugins/element-extends/mixins/search.js'

import MoreAction from './MoreAction/index.vue'

function getDefaultModel() {
  const model = this.sheetMixin.getFormatModel() || []

  const value = {
    ...(model.length
      ? {
        ...model.reduce((obj, item) => {
          obj[item.field] = getNestedValue(item, 'value', 'search')
          return obj
        }, {})
      }
      : {}),
    ...this.$props.searchMixin.model
  }

  return value
}

export default {
  name: 'EleSheetSearch',
  inheritAttrs: false,
  mixins: [sheetMixin({ scope: 'search' })],
  components: {
    MoreAction
  },
  props: {
    load: {
      type: Function,
      default: void 0
    },
    searchClass: {
      type: [String, Object, Array],
      default: void 0
    },
    actionClass: {
      type: [String, Object, Array],
      default: void 0
    },
    searchMixin: {
      type: Object,
      default: () => searchMixin().data().searchMixin
    }
  },
  data() {
    return {}
  },
  watch: {
    'searchMixin.model': {
      handler(value) {
        this.$emit('change', value)
      },
      deep: true
    }
  },
  created() {
    if (this.$props.load) this.searchMixin.load = this.$props.load
    this.searchMixin.init(getDefaultModel.call(this))
  },
  methods: {
    getItemProps(data = {}) {
      const props = this.sheetMixin.createProps(data, {
        label: void 0,
        field: void 0,
        deepKeys: 'formItemProps'
      })

      const value = {
        ...props,
        prop: props.field
      }

      return value
    },
    getFieldProps(data) {
      const value = {
        type: 'text',
        ...this.sheetMixin.getFieldProps(data)
      }

      return value
    },

    getSlotProps(data) {
      const value = {
        model: this.searchMixin.model,
        query: this.query,
        reset: this.reset
      }

      if (data) {
        const itemProps = this.getItemProps(data)
        const fieldProps = this.getFieldProps(data)

        Object.assign(value, {
          ...itemProps,
          itemProps,
          fieldProps
        })
      }

      return value
    },

    async query() {
      await this.searchMixin.query()
    },
    async reset() {
      await this.searchMixin.reset()
    },
    init(value) {
      this.searchMixin.init(value)
    }
  }
}
</script>

<style></style>
