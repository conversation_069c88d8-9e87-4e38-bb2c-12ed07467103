<template>
  <div class="service-monitor-container">
    <!-- 顶部控制区 -->
    <div class="header-controls">
      <el-card class="control-card">
        <div class="flex justify-between items-center">
          <div class="time-controls">
            <span class="control-label">时间范围：</span>
            <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
              <el-radio-button label="today">今日</el-radio-button>
              <el-radio-button label="week">本周</el-radio-button>
              <el-radio-button label="custom">自定义</el-radio-button>
            </el-radio-group>
            <el-date-picker
              v-if="timeRange === 'custom'"
              v-model="customTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              class="ml-3"
              @change="handleCustomTimeChange"
            />
          </div>
          <div class="export-controls">
            <el-button type="primary" icon="el-icon-download" @click="handleExport">
              导出报表
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 关键指标概览区 -->
    <div class="metrics-overview">
      <el-row :gutter="20">
        <el-col v-for="(metric, index) in keyMetrics" :key="index" :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-icon" :class="metric.color">
                <i :class="metric.icon"></i>
              </div>
              <div class="metric-info">
                <div class="metric-label">{{ metric.label }}</div>
                <div class="metric-value" :class="{ error: metric.isError }">
                  {{ metric.value }}
                  <i v-if="metric.isError" class="el-icon-warning-outline error-icon"></i>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表展示区 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header">
              <span><i class="el-icon-data-line"></i> 实时带宽趋势</span>
            </div>
            <div ref="bandwidthChart" class="chart-container"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header">
              <span><i class="el-icon-connection"></i> 客户端连接数趋势</span>
            </div>
            <div ref="connectionChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="data-table-section">
      <el-card class="table-card">
        <div slot="header">
          <span><i class="el-icon-s-data"></i> 详细数据统计</span>
        </div>
        <el-table v-loading="tableLoading" :data="tableData" border stripe style="width: 100%">
          <el-table-column prop="time" label="统计时间" width="180" align="center">
            <template slot-scope="scope">
              {{ formatTime(scope.row.time) }}
            </template>
          </el-table-column>
          <el-table-column prop="protocol" label="协议类型" width="120" align="center">
            <template slot-scope="scope">
              <el-tag :type="getProtocolTagType(scope.row.protocol)">
                {{ scope.row.protocol }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="bandwidth" label="实时带宽" width="150" align="center">
            <template slot-scope="scope"> {{ scope.row.bandwidth }} Mbps </template>
          </el-table-column>
          <el-table-column prop="connections" label="客户端连接数" width="150" align="center">
            <template slot-scope="scope">
              {{ scope.row.connections }}
            </template>
          </el-table-column>
          <el-table-column prop="totalTraffic" label="总流量" width="150" align="center">
            <template slot-scope="scope"> {{ scope.row.totalTraffic }} GB </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 'normal' ? 'success' : 'danger'">
                {{ scope.row.status === 'normal' ? '正常' : '异常' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import dayjs from 'dayjs'

export default {
  name: 'ServiceMonitor',
  data() {
    return {
      // 时间范围控制
      timeRange: 'today',
      customTimeRange: [],

      // 图表实例
      bandwidthChart: null,
      connectionChart: null,

      // 加载状态
      loading: false,
      tableLoading: false,

      // 关键指标数据
      keyMetrics: [
        {
          label: '平均带宽',
          value: '2.3 Mbps',
          icon: 'el-icon-data-line',
          color: 'metric-blue',
          isError: false
        },
        {
          label: '当前连接数',
          value: '12',
          icon: 'el-icon-connection',
          color: 'metric-green',
          isError: false
        },
        {
          label: '今日总流量',
          value: '48 GB',
          icon: 'el-icon-pie-chart',
          color: 'metric-orange',
          isError: false
        },
        {
          label: '异常连接数',
          value: '0',
          icon: 'el-icon-warning-outline',
          color: 'metric-red',
          isError: false
        }
      ],

      // 图表数据
      bandwidthData: {
        times: [],
        values: []
      },
      connectionData: {
        times: [],
        values: []
      },

      // 表格数据
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      }
    }
  },

  mounted() {
    this.initCharts()
    this.loadData()
    this.setupAutoRefresh()

    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },

  beforeDestroy() {
    // 清理定时器和事件监听
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
    window.removeEventListener('resize', this.handleResize)

    // 销毁图表实例
    if (this.bandwidthChart) {
      this.bandwidthChart.dispose()
    }
    if (this.connectionChart) {
      this.connectionChart.dispose()
    }
  },

  methods: {
    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        this.initBandwidthChart()
        this.initConnectionChart()
      })
    },

    // 初始化带宽趋势图
    initBandwidthChart() {
      this.bandwidthChart = echarts.init(this.$refs.bandwidthChart)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: function(params) {
            const param = params[0]
            return `${param.name}<br/>带宽: ${param.value} Mbps`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.bandwidthData.times,
          axisLabel: {
            formatter: function(value) {
              return dayjs(value).format('HH:mm')
            }
          }
        },
        yAxis: {
          type: 'value',
          name: 'Mbps',
          axisLabel: {
            formatter: '{value}'
          }
        },
        series: [
          {
            name: '带宽',
            type: 'line',
            smooth: true,
            data: this.bandwidthData.values,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                  { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
                ]
              }
            }
          }
        ]
      }

      this.bandwidthChart.setOption(option)
    },
    // 初始化连接数趋势图
    initConnectionChart() {
      this.connectionChart = echarts.init(this.$refs.connectionChart)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: function(params) {
            const param = params[0]
            return `${param.name}<br/>连接数: ${param.value}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.connectionData.times,
          axisLabel: {
            formatter: function(value) {
              return dayjs(value).format('HH:mm')
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '连接数',
          axisLabel: {
            formatter: '{value}'
          }
        },
        series: [
          {
            name: '连接数',
            type: 'line',
            smooth: true,
            data: this.connectionData.values,
            itemStyle: {
              color: '#67C23A'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
                  { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
                ]
              }
            }
          }
        ]
      }

      this.connectionChart.setOption(option)
    },

    // 更新图表数据
    updateCharts() {
      if (this.bandwidthChart) {
        this.bandwidthChart.setOption({
          xAxis: {
            data: this.bandwidthData.times
          },
          series: [
            {
              data: this.bandwidthData.values
            }
          ]
        })
      }

      if (this.connectionChart) {
        this.connectionChart.setOption({
          xAxis: {
            data: this.connectionData.times
          },
          series: [
            {
              data: this.connectionData.values
            }
          ]
        })
      }
    },

    // 处理窗口大小变化
    handleResize() {
      this.$nextTick(() => {
        if (this.bandwidthChart) {
          this.bandwidthChart.resize()
        }
        if (this.connectionChart) {
          this.connectionChart.resize()
        }
      })
    },

    // 时间范围变化处理
    handleTimeRangeChange(value) {
      this.timeRange = value
      if (value !== 'custom') {
        this.customTimeRange = []
      }
      this.loadData()
    },

    // 自定义时间范围变化处理
    handleCustomTimeChange(value) {
      this.customTimeRange = value
      this.loadData()
    },

    // 加载数据
    async loadData() {
      this.loading = true
      this.tableLoading = true

      try {
        // 获取时间范围
        const timeParams = this.getTimeParams()

        // 并行加载所有数据
        await Promise.all([
          this.loadMetricsData(timeParams),
          this.loadChartData(timeParams),
          this.loadTableData(timeParams)
        ])

        // 更新图表
        this.updateCharts()
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败，请稍后重试')
      } finally {
        this.loading = false
        this.tableLoading = false
      }
    },

    // 获取时间参数
    getTimeParams() {
      const now = dayjs()
      let startTime, endTime

      switch (this.timeRange) {
        case 'today':
          startTime = now.startOf('day').format('YYYY-MM-DD HH:mm:ss')
          endTime = now.format('YYYY-MM-DD HH:mm:ss')
          break
        case 'week':
          startTime = now.startOf('week').format('YYYY-MM-DD HH:mm:ss')
          endTime = now.format('YYYY-MM-DD HH:mm:ss')
          break
        case 'custom':
          if (this.customTimeRange && this.customTimeRange.length === 2) {
            startTime = this.customTimeRange[0]
            endTime = this.customTimeRange[1]
          } else {
            startTime = now.startOf('day').format('YYYY-MM-DD HH:mm:ss')
            endTime = now.format('YYYY-MM-DD HH:mm:ss')
          }
          break
        default:
          startTime = now.startOf('day').format('YYYY-MM-DD HH:mm:ss')
          endTime = now.format('YYYY-MM-DD HH:mm:ss')
      }

      return { startTime, endTime }
    },
    // 加载关键指标数据
    async loadMetricsData(timeParams) {
      // TODO: 替换为实际的API调用
      // const response = await this.$http.get('/api/service/metrics', { params: timeParams })

      // 模拟数据
      const mockData = {
        avgBandwidth: '2.3',
        currentConnections: 12,
        totalTraffic: '48',
        abnormalConnections: 0
      }

      // 更新关键指标
      this.keyMetrics[0].value = `${mockData.avgBandwidth} Mbps`
      this.keyMetrics[1].value = mockData.currentConnections.toString()
      this.keyMetrics[2].value = `${mockData.totalTraffic} GB`
      this.keyMetrics[3].value = mockData.abnormalConnections.toString()
      this.keyMetrics[3].isError = mockData.abnormalConnections > 0
    },

    // 加载图表数据
    async loadChartData(timeParams) {
      // TODO: 替换为实际的API调用
      // const response = await this.$http.get('/api/service/charts', { params: timeParams })

      // 生成模拟数据
      const now = dayjs()
      const times = []
      const bandwidthValues = []
      const connectionValues = []

      // 生成最近24小时的数据，每15分钟一个点
      for (let i = 0; i < 96; i++) {
        const time = now.subtract(i * 15, 'minute').format('YYYY-MM-DD HH:mm:ss')
        times.unshift(time)

        // 模拟带宽数据 (1.5-3.5 Mbps)
        bandwidthValues.unshift((1.5 + Math.random() * 2).toFixed(1))

        // 模拟连接数数据 (8-20)
        connectionValues.unshift(Math.floor(8 + Math.random() * 12))
      }

      this.bandwidthData = { times, values: bandwidthValues }
      this.connectionData = { times, values: connectionValues }
    },

    // 加载表格数据
    async loadTableData(timeParams) {
      // TODO: 替换为实际的API调用
      // const response = await this.$http.get('/api/service/table', {
      //   params: {
      //     ...timeParams,
      //     page: this.pagination.currentPage,
      //     size: this.pagination.pageSize
      //   }
      // })

      // 生成模拟表格数据
      const mockTableData = []
      const protocols = ['RTSPS', 'RTMP', 'HLS', 'WebRTC']
      const now = dayjs()

      for (let i = 0; i < this.pagination.pageSize; i++) {
        mockTableData.push({
          id: i + 1,
          time: now.subtract(i * 5, 'minute').format('YYYY-MM-DD HH:mm:ss'),
          protocol: protocols[Math.floor(Math.random() * protocols.length)],
          bandwidth: (1.5 + Math.random() * 2).toFixed(1),
          connections: Math.floor(5 + Math.random() * 10),
          totalTraffic: (Math.random() * 5).toFixed(1),
          status: Math.random() > 0.1 ? 'normal' : 'abnormal'
        })
      }

      this.tableData = mockTableData
      this.pagination.total = 200 // 模拟总数
    },

    // 设置自动刷新
    setupAutoRefresh() {
      // 每30秒刷新一次数据
      this.refreshTimer = setInterval(() => {
        this.loadData()
      }, 30000)
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.currentPage = 1
      this.loadTableData(this.getTimeParams())
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.loadTableData(this.getTimeParams())
    },

    // 格式化时间显示
    formatTime(time) {
      return dayjs(time).format('MM-DD HH:mm:ss')
    },

    // 获取协议标签类型
    getProtocolTagType(protocol) {
      const typeMap = {
        RTSPS: 'primary',
        RTMP: 'success',
        HLS: 'warning',
        WebRTC: 'info'
      }
      return typeMap[protocol] || 'default'
    },

    // 导出报表
    async handleExport() {
      try {
        this.$message.info('正在生成报表，请稍候...')

        // 获取当前时间参数
        const timeParams = this.getTimeParams()

        // TODO: 替换为实际的导出API调用
        // const filename = `service_monitor_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`
        // await this.download('/api/service/export', timeParams, filename)

        // 模拟导出功能
        setTimeout(() => {
          this.$message.success('报表导出成功！')
        }, 2000)
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请稍后重试')
      }
    }
  }
}
</script>
<style scoped>
  .service-monitor-container {
    min-height: 100vh;
  }

  /* 顶部控制区样式 */
  .header-controls {
    margin-bottom: 20px;
  }

  .control-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .control-label {
    font-weight: 500;
    color: #606266;
    margin-right: 12px;
  }

  .time-controls {
    display: flex;
    align-items: center;
  }

  .export-controls {
    display: flex;
    align-items: center;
  }

  /* 关键指标概览区样式 */
  .metrics-overview {
    margin-bottom: 20px;
  }

  .metric-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }

  .metric-content {
    display: flex;
    align-items: center;
    padding: 10px 0;
  }

  .metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
  }

  .metric-blue {
    background: linear-gradient(135deg, #409eff, #66b1ff);
  }

  .metric-green {
    background: linear-gradient(135deg, #67c23a, #85ce61);
  }

  .metric-orange {
    background: linear-gradient(135deg, #e6a23c, #eebe77);
  }

  .metric-red {
    background: linear-gradient(135deg, #f56c6c, #f78989);
  }

  .metric-info {
    flex: 1;
  }

  .metric-label {
    font-size: 14px;
    color: #909399;
    margin-bottom: 5px;
  }

  .metric-value {
    font-size: 24px;
    font-weight: bold;
    color: #303133;
    display: flex;
    align-items: center;
  }

  .metric-value.error {
    color: #f56c6c;
  }

  .error-icon {
    margin-left: 8px;
    font-size: 18px;
    color: #f56c6c;
  }

  /* 图表区域样式 */
  .charts-section {
    margin-bottom: 20px;
  }

  .chart-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .chart-container {
    height: 350px;
    width: 100%;
  }

  /* 数据表格区域样式 */
  .data-table-section {
    margin-bottom: 20px;
  }

  .table-card {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .metric-content {
      flex-direction: column;
      text-align: center;
    }

    .metric-icon {
      margin-right: 0;
      margin-bottom: 10px;
    }
  }

  @media (max-width: 768px) {
    .service-monitor-container {
      padding: 10px;
    }

    .control-card .el-card__body {
      padding: 15px;
    }

    .time-controls {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .export-controls {
      margin-top: 10px;
    }

    .chart-container {
      height: 250px;
    }

    .metric-value {
      font-size: 20px;
    }
  }

  /* Element UI 组件样式覆盖 */
  .el-card__header {
    background-color: #fafafa;
    border-bottom: 1px solid #ebeef5;
    font-weight: 500;
    color: #303133;
  }

  .el-table {
    border-radius: 4px;
    overflow: hidden;
  }

  .el-table th {
    background-color: #fafafa;
    color: #606266;
    font-weight: 500;
  }

  .el-table--border td,
  .el-table--border th {
    border-right: 1px solid #ebeef5;
  }

  .el-pagination {
    text-align: right;
  }

  /* 工具提示样式 */
  .flex {
    display: flex;
  }

  .justify-between {
    justify-content: space-between;
  }

  .items-center {
    align-items: center;
  }

  .ml-3 {
    margin-left: 12px;
  }

  /* 加载状态样式 */
  .el-loading-mask {
    border-radius: 8px;
  }

  /* 动画效果 */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .metric-card,
  .chart-card,
  .table-card {
    animation: fadeInUp 0.6s ease-out;
  }

  .metric-card:nth-child(1) {
    animation-delay: 0.1s;
  }
  .metric-card:nth-child(2) {
    animation-delay: 0.2s;
  }
  .metric-card:nth-child(3) {
    animation-delay: 0.3s;
  }
  .metric-card:nth-child(4) {
    animation-delay: 0.4s;
  }
</style>
