<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <!-- 工具栏自定义按钮 -->
    <template #toolbar:after>
    </template>

    <!-- 状态列自定义渲染 -->
    <template #table:value3:simple="{ row }">
      <el-tag :type="getStatusTagType(row.value3)">
        {{ row.value3 }}
      </el-tag>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <el-button type="text" size="mini" @click="handleEdit(row)">算法列表</el-button>
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { templateStatus } from '@/dicts/video/index.js'

export default {
  name: 'VideoProcessTemplate',
  data() {
    return {
      tableType: 'template_orchestration_management'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '处理模板',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          // 任务模板名称
          value1: {
            type: 'text',
            label: '任务模板名称',
            align: 'left',
            width: 200,
            search: {
              placeholder: '请输入任务模板名称'
            },
            form: {
              required: true,
              placeholder: '请输入任务模板名称'
            }
          },
          // 数量
          value2: {
            type: 'number',
            label: '数量',
            width: 100,
            search: {
              hidden: true
            },
            form: {
              placeholder: '请输入数量',
              min: 0
            }
          },
          // 状态
          value3: {
            type: 'select',
            label: '状态',
            width: 100,
            search: {
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                ...templateStatus
              ]
            },
            form: {
              required: true
            },
            options: templateStatus
          },
          // 描述
          value4: {
            type: 'textarea',
            label: '描述',
            search: {
              hidden: true
            },
            form: {
              placeholder: '请输入模板描述',
              rows: 3
            }
          },
          // 创建人
          value5: {
            type: 'text',
            label: '创建人',
            width: 120,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          },
          // 创建时间
          value6: {
            type: 'datetime',
            label: '创建时间',
            width: 160,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          }
        }
      }
    }
  },
  methods: {
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '启用': 'success',
        '停用': 'info'
      }
      return statusMap[status] || 'info'
    },

    // 编辑模板
    handleEdit(row) {
      this.$refs.sheetRef.handleEdit(row)
    },

    // 复制模板
    handleCopy(row) {
      this.$modal.confirm(`确认要复制模板"${row.value1}"吗？`).then(() => {
        const copyData = {
          ...row,
          value1: `${row.value1}_副本`,
          id: undefined // 清除ID，作为新记录添加
        }

        request({
          url: '/system/AutoOsmotic',
          method: 'post',
          data: {
            ...copyData,
            type: this.tableType
          }
        }).then(() => {
          this.$modal.msgSuccess('复制成功')
          this.$refs.sheetRef.getTableData()
        }).catch(() => {
          this.$modal.msgError('复制失败')
        })
      }).catch(() => {})
    },

    // 删除模板
    handleDelete(row) {
      this.$modal.confirm(`确认要删除模板"${row.value1}"吗？`).then(() => {
        request({
          url: `/system/AutoOsmotic/${row.id}`,
          method: 'delete'
        }).then(() => {
          this.$modal.msgSuccess('删除成功')
          this.$refs.sheetRef.getTableData()
        }).catch(() => {
          this.$modal.msgError('删除失败')
        })
      }).catch(() => {})
    },

    // 批量导入
    handleImport() {
      this.$modal.msgInfo('批量导入功能开发中...')
    },

    // 批量导出
    handleExport() {
      this.$modal.msgInfo('批量导出功能开发中...')
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
