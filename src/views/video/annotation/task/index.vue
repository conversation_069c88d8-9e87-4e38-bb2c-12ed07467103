<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <!-- 工具栏自定义按钮 -->
    <template #toolbar:after>
    </template>

    <!-- 状态列自定义渲染 -->
    <template #table:value9:simple="{ row }">
      <el-tag :type="getStatusTagType(row.value9)">
        {{ row.value9 }}
      </el-tag>
    </template>

    <!-- 标注主题列自定义渲染 -->
    <template #table:value3:simple="{ row }">
      <el-tag type="primary">
        {{ row.value3 }}
      </el-tag>
    </template>

    <!-- 调度方式列自定义渲染 -->
    <template #table:value4:simple="{ row }">
      <el-tag type="info">
        {{ row.value4 }}
      </el-tag>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <el-button
        v-if="row.value9 === '运行中'"
        type="text"
        size="mini"
        @click="handlePause(row)"
      >
        暂停
      </el-button>
      <el-button
        v-if="row.value9 === '已停止' || row.value9 === '待执行'"
        type="text"
        size="mini"
        @click="handleStart(row)"
      >
        启动
      </el-button>
      <el-button
        v-if="row.value9 === '已完成'"
        type="text"
        size="mini"
        @click="handleViewDetails(row)"
      >
        查看详情
      </el-button>
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { annotationTheme, scheduleType, annotationTaskStatus } from '@/dicts/video/index.js'

export default {
  name: 'VideoAnnotationTask',
  data() {
    return {
      tableType: 'annotation_task_management'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '视频标注任务',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          // 序号 - 自动生成
          value1: {
            type: 'text',
            label: '任务ID',
            align: 'left',
            width: 120,
            search: {
              hidden: true
            }
          },
          // 视频流名称
          value2: {
            type: 'text',
            label: '视频流名称',
            align: 'left',
            width: 200,
            search: {
              placeholder: '请输入视频流名称'
            }
          },
          // 标注主题
          value3: {
            type: 'select',
            label: '标注主题',
            width: 120,
            search: {
              type: 'select',
              options: [
                { label: '全部主题', value: '' },
                ...annotationTheme
              ]
            },
            options: annotationTheme
          },
          // 调度方式
          value4: {
            type: 'select',
            label: '调度方式',
            width: 120,
            search: {
              type: 'select',
              options: [
                { label: '全部方式', value: '' },
                ...scheduleType
              ]
            },
            options: scheduleType
          },
          // 调度时间
          value5: {
            type: 'datetime',
            label: '调度时间',
            width: 160,
            search: {
              hidden: true
            }
          },
          // CRON表达式
          value6: {
            type: 'text',
            label: 'CRON表达式',
            width: 150,
            search: {
              hidden: true
            }
          },
          // 视频识别结果
          value7: {
            type: 'text',
            label: '视频识别结果',
            width: 200,
            search: {
              hidden: true
            }
          },
          // 创建时间
          value8: {
            type: 'datetime',
            label: '创建时间',
            width: 160,
            search: {
              hidden: true
            }
          },
          // 状态
          value9: {
            type: 'select',
            label: '状态',
            width: 100,
            search: {
              hidden: true
            },
            options: annotationTaskStatus
          }
        }
      }
    }
  },
  methods: {
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '运行中': 'success',
        '已完成': 'success',
        '已停止': 'info',
        '待执行': 'warning'
      }
      return statusMap[status] || 'info'
    },

    // 创建新任务
    handleCreateTask() {
      this.$refs.sheetRef.handleAdd()
    },

    // 暂停任务
    handlePause(row) {
      this.$modal.confirm(`确认要暂停任务"${row.value2}"吗？`).then(() => {
        // 这里可以调用暂停接口
        console.log('暂停任务:', row)
        this.$modal.msgSuccess('任务已暂停')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    },

    // 启动任务
    handleStart(row) {
      this.$modal.confirm(`确认要启动任务"${row.value2}"吗？`).then(() => {
        // 这里可以调用启动接口
        console.log('启动任务:', row)
        this.$modal.msgSuccess('任务已启动')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    },

    // 查看详情
    handleViewDetails(row) {
      // 这里可以跳转到详情页面或打开详情弹窗
      console.log('查看任务详情:', row)
      this.$modal.msgInfo('查看任务详情功能待实现')
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
